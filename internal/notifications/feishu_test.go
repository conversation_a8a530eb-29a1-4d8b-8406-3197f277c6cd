package notifications

import (
	"testing"
	"time"

	"alarm_distribution/internal/config"
	"alarm_distribution/internal/models"
)

func TestFeishuChannel_getReceiverIDByRegion(t *testing.T) {
	// Setup test configuration
	cfg := &config.FeishuConfig{
		AppID:           "test_app_id",
		AppSecret:       "test_app_secret",
		Enabled:         true,
		DefaultReceiver: "ou_default",
		RegionReceivers: map[string]string{
			"cn-hangzhou":      "ou_demo_cn_hangzhou",
			"ap-southeast-1":   "ou_demo_sg",
			"ap-east-1":        "ou_demo_hk",
		},
	}

	// Create FeishuChannel instance
	channel := NewFeishuChannel(cfg)

	tests := []struct {
		name           string
		alert          *models.Alert
		expectedReceiver string
	}{
		{
			name: "Aliyun ARMS alert with cn-hangzhou region",
			alert: &models.Alert{
				ID:          "test1",
				ProjectName: "aliyun_arms",
				ProjectType: "aliyun_arms",
				Status:      models.AlertStatusFiring,
				Severity:    models.AlertSeverityError,
				AlertName:   "Test Alert",
				Message:     "Test message",
				Labels: map[string]string{
					"_aliyun_arms_region_id": "cn-hangzhou",
					"alertname":              "Test Alert",
				},
				StartsAt:  time.Now(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			expectedReceiver: "ou_demo_cn_hangzhou",
		},
		{
			name: "Aliyun ARMS alert with Singapore region",
			alert: &models.Alert{
				ID:          "test2",
				ProjectName: "aliyun_arms",
				ProjectType: "aliyun_arms",
				Status:      models.AlertStatusFiring,
				Severity:    models.AlertSeverityError,
				AlertName:   "Test Alert SG",
				Message:     "Test message",
				Labels: map[string]string{
					"_aliyun_arms_region_id": "ap-southeast-1",
					"alertname":              "Test Alert SG",
				},
				StartsAt:  time.Now(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			expectedReceiver: "ou_demo_sg",
		},
		{
			name: "Aliyun ARMS alert with Hong Kong region",
			alert: &models.Alert{
				ID:          "test3",
				ProjectName: "aliyun_arms",
				ProjectType: "aliyun_arms",
				Status:      models.AlertStatusFiring,
				Severity:    models.AlertSeverityError,
				AlertName:   "Test Alert HK",
				Message:     "Test message",
				Labels: map[string]string{
					"_aliyun_arms_region_id": "ap-east-1",
					"alertname":              "Test Alert HK",
				},
				StartsAt:  time.Now(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			expectedReceiver: "ou_demo_hk",
		},
		{
			name: "Alert with unknown region - should use default",
			alert: &models.Alert{
				ID:          "test4",
				ProjectName: "aliyun_arms",
				ProjectType: "aliyun_arms",
				Status:      models.AlertStatusFiring,
				Severity:    models.AlertSeverityError,
				AlertName:   "Test Alert Unknown",
				Message:     "Test message",
				Labels: map[string]string{
					"_aliyun_arms_region_id": "unknown-region",
					"alertname":              "Test Alert Unknown",
				},
				StartsAt:  time.Now(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			expectedReceiver: "ou_default",
		},
		{
			name: "Alert without region - should use default",
			alert: &models.Alert{
				ID:          "test5",
				ProjectName: "prometheus",
				ProjectType: "alertmanager",
				Status:      models.AlertStatusFiring,
				Severity:    models.AlertSeverityError,
				AlertName:   "Test Alert No Region",
				Message:     "Test message",
				Labels: map[string]string{
					"alertname": "Test Alert No Region",
				},
				StartsAt:  time.Now(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			expectedReceiver: "ou_default",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			receiverID := channel.getReceiverIDByRegion(tt.alert)
			if receiverID != tt.expectedReceiver {
				t.Errorf("getReceiverIDByRegion() = %v, want %v", receiverID, tt.expectedReceiver)
			}
		})
	}
}

func TestFeishuChannel_extractRegionFromAlert(t *testing.T) {
	cfg := &config.FeishuConfig{
		AppID:           "test_app_id",
		AppSecret:       "test_app_secret",
		Enabled:         true,
		DefaultReceiver: "ou_default",
	}

	channel := NewFeishuChannel(cfg)

	tests := []struct {
		name           string
		alert          *models.Alert
		expectedRegion string
	}{
		{
			name: "Aliyun ARMS region extraction",
			alert: &models.Alert{
				Labels: map[string]string{
					"_aliyun_arms_region_id": "cn-hangzhou",
				},
			},
			expectedRegion: "cn-hangzhou",
		},
		{
			name: "Generic region label",
			alert: &models.Alert{
				Labels: map[string]string{
					"region": "us-west-1",
				},
			},
			expectedRegion: "us-west-1",
		},
		{
			name: "AWS region label",
			alert: &models.Alert{
				Labels: map[string]string{
					"aws_region": "us-east-1",
				},
			},
			expectedRegion: "us-east-1",
		},
		{
			name: "No region labels",
			alert: &models.Alert{
				Labels: map[string]string{
					"alertname": "test",
				},
			},
			expectedRegion: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			region := channel.extractRegionFromAlert(tt.alert)
			if region != tt.expectedRegion {
				t.Errorf("extractRegionFromAlert() = %v, want %v", region, tt.expectedRegion)
			}
		})
	}
}
